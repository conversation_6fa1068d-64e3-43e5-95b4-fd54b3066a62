using System.Threading.Tasks;
using AlpacaMomentumBot.Services;

namespace AlpacaMomentumBot.Examples;

public static class RunAsyncDemo
{
    public static async Task RunAsync()
    {
        // This is just a playground example; it isn't wired to DI.
        var signalGenerator = default(ISignalGenerator);

        // Pretend we call the generator
        if (signalGenerator != null)
        {
            var signals = await signalGenerator.RunAsync(5);
            // ...do something with signals
        }
    }
}
