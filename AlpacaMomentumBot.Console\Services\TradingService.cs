using Serilog;

namespace SmaTrendFollower.Services;

public sealed class TradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly ITradeExecutor _executor;

    public TradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        ITradeExecutor executor)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _executor = executor;
    }

    public Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        var signals = _signalGenerator.GenerateSignals(10);
        foreach (var sig in signals)
        {
            var sizing = _riskManager.CalculateSizing(sig);
            _executor.Execute(sig, sizing);
        }
        Log.Information("Cycle complete.");
        return Task.CompletedTask;
    }
}
