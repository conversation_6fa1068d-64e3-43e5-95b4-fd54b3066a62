using Serilog;

namespace SmaTrendFollower.Services;

public sealed class FileUniverseProvider : IUniverseProvider
{
    private const string UniverseFile = "universe.csv";

    public IEnumerable<string> GetSymbols()
    {
        if (File.Exists(UniverseFile))
        {
            var symbols = File.ReadAllLines(UniverseFile)
                .Select(line => line.Trim().ToUpperInvariant())
                .Where(s => !string.IsNullOrWhiteSpace(s));
            return symbols;
        }

        Log.Warning("{File} not found. Using default universe.", UniverseFile);
        return new[] { "SPY", "QQQ", "AAPL", "MSFT", "NVDA" };
    }
}
