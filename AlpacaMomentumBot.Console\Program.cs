using DotNetEnv;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Threading.Tasks;

namespace AlpacaMomentumBot.ConsoleApp;   // pick any namespace you like

internal static class Program
{
    private static async Task Main(string[] args)
    {
        // Load .env variables (optional)
        Env.Load();

        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .WriteTo.File("logs/bot-.log", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // Basic Host/DI bootstrap
        using IHost host = Host.CreateDefaultBuilder(args)
            .ConfigureServices(services =>
            {
                // register your services here
                // services.AddSingleton<ISignalGenerator, MySignalGenerator>();
            })
            .UseSerilog()
            .Build();

        // do whatever the bot needs at startup
        Log.Information("Bot started");

        // Example: keep host alive until Ctrl-C
        await host.RunAsync();
    }
}
