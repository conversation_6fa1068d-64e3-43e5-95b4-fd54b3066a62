using Skender.Stock.Indicators;

namespace SmaTrendFollower.Services;

public sealed class SimpleSmaSignalGenerator : ISignalGenerator
{
    private readonly IUniverseProvider _universe;

    public SimpleSmaSignalGenerator(IUniverseProvider universe)
    {
        _universe = universe;
    }

    public IEnumerable<TradingSignal> GenerateSignals(int topN = 10)
    {
        // Dummy data for demo purposes
        var rnd = new Random();
        var signals = _universe.GetSymbols()
            .Select(sym => new TradingSignal(sym, rnd.Next(50, 500), rnd.Next(1, 5)))
            .OrderByDescending(s => s.Price)
            .Take(topN);
        return signals;
    }
}
